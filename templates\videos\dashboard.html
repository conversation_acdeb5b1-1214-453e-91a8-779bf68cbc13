{% extends 'base.html' %}

{% block title %}Dashboard - EagleView{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="fas fa-home me-3 text-primary float"></i>
                    Dashboard
                </h1>
                <p class="text-muted mb-0">Bem-vindo ao EagleView - Sua plataforma de timelapses profissionais</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'frame_editor' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Novo Projeto
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Estatísticas Modernas -->
<div class="row mb-5">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card h-100 stat-card">
            <div class="card-body text-center position-relative">
                <div class="stat-icon mb-3">
                    <div class="icon-circle bg-gradient-primary">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
                <h2 class="stat-number mb-2">{{ total_timelapses }}</h2>
                <p class="stat-label text-muted mb-0">Timelapses Criados</p>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up text-success me-1"></i>
                    <small class="text-success">Total de projetos</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card h-100 stat-card">
            <div class="card-body text-center position-relative">
                <div class="stat-icon mb-3">
                    <div class="icon-circle bg-gradient-info">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                </div>
                <h2 class="stat-number mb-2">{{ processing_jobs }}</h2>
                <p class="stat-label text-muted mb-0">Em Processamento</p>
                <div class="stat-trend">
                    {% if processing_jobs > 0 %}
                        <i class="fas fa-spinner fa-spin text-warning me-1"></i>
                        <small class="text-warning">Processando agora</small>
                    {% else %}
                        <i class="fas fa-check text-success me-1"></i>
                        <small class="text-success">Todos concluídos</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-12 mb-3">
        <div class="card h-100 stat-card">
            <div class="card-body text-center position-relative">
                <div class="stat-icon mb-3">
                    <div class="icon-circle bg-gradient-orange">
                        <i class="fas fa-user-circle fa-2x"></i>
                    </div>
                </div>
                <h4 class="stat-number mb-2 text-truncate">{{ user.get_full_name|default:user.username }}</h4>
                <p class="stat-label text-muted mb-0">Usuário Ativo</p>
                <div class="stat-trend">
                    <i class="fas fa-clock text-info me-1"></i>
                    <small class="text-info">Online agora</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas Modernas -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card quick-actions-card">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <i class="fas fa-bolt me-3 text-warning"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <a href="{% url 'frame_editor' %}" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center action-btn">
                            <i class="fas fa-plus-circle fa-2x mb-3"></i>
                            <span class="fw-bold">Criar Novo Timelapse</span>
                            <small class="text-light mt-2 opacity-75">Comece um novo projeto</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{% url 'timelapse_list' %}" class="btn btn-outline-light btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center action-btn">
                            <i class="fas fa-folder-open fa-2x mb-3"></i>
                            <span class="fw-bold">Ver Projetos</span>
                            <small class="text-muted mt-2">Gerencie seus timelapses</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{% url 'server_settings' %}" class="btn btn-outline-light btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center action-btn">
                            <i class="fas fa-server fa-2x mb-3"></i>
                            <span class="fw-bold">Configurar Servidores</span>
                            <small class="text-muted mt-2">Conectar fontes de imagens</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        {% if user.is_staff %}
                        <a href="/admin/" class="btn btn-outline-light btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center action-btn">
                            <i class="fas fa-cog fa-2x mb-3"></i>
                            <span class="fw-bold">Administração</span>
                            <small class="text-muted mt-2">Configurações avançadas</small>
                        </a>
                        {% else %}
                        <div class="btn btn-outline-secondary btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center disabled">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <span class="fw-bold">Suporte</span>
                            <small class="text-muted mt-2">Ajuda e documentação</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Timelapses Recentes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Timelapses Recentes
                </h5>
                <a href="{% url 'timelapse_list' %}" class="btn btn-outline-primary btn-sm">
                    Ver Todos
                </a>
            </div>
            <div class="card-body">
                {% if recent_timelapses %}
                    <div class="row g-4">
                        {% for timelapse in recent_timelapses %}
                        <div class="col-xl-4 col-lg-6 mb-4">
                            <div class="card h-100 project-card">
                                {% if timelapse.video %}
                                    <div class="project-thumbnail mb-3">
                                        <video class="img-fluid rounded-top" style="width: 100%; height: 180px; object-fit: cover;" muted>
                                            <source src="{{ timelapse.video.url }}" type="video/mp4">
                                        </video>
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                Concluído
                                            </span>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="project-thumbnail mb-3 d-flex align-items-center justify-content-center bg-secondary rounded-top" style="height: 180px;">
                                        <div class="text-center">
                                            <i class="fas fa-video fa-3x text-muted mb-2"></i>
                                            <p class="text-muted small mb-0">Processando...</p>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title fw-bold mb-3">{{ timelapse.title }}</h6>
                                    
                                    <div class="project-meta mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar text-primary me-2"></i>
                                            <small class="text-muted">{{ timelapse.created_at|date:"d/m/Y H:i" }}</small>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-user text-info me-2"></i>
                                            <small class="text-muted">{{ timelapse.created_by.get_full_name|default:timelapse.created_by.username }}</small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-images text-warning me-2"></i>
                                            <small class="text-muted">{{ timelapse.frames.count }} frame{{ timelapse.frames.count|pluralize }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <div class="d-grid gap-2">
                                            <a href="{% url 'timelapse_detail' timelapse.pk %}" class="btn btn-primary">
                                                <i class="fas fa-eye me-2"></i>
                                                Ver Detalhes
                                            </a>
                                            {% if timelapse.video %}
                                                <a href="{{ timelapse.video.url }}" download="{{ timelapse.title }}.mp4" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-download me-2"></i>
                                                    Download
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-video fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum timelapse criado ainda</h5>
                        <p class="text-muted">Comece criando seu primeiro projeto!</p>
                        <a href="{% url 'frame_editor' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Criar Timelapse
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Dicas e Informações Modernas -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card tips-card">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <i class="fas fa-lightbulb me-3 text-warning"></i>
                    Dicas Profissionais
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-images"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">Qualidade das Imagens</h6>
                                    <p class="text-muted small mb-0">Use imagens de alta resolução (mínimo 1080p) para resultados profissionais. Mantenha a mesma resolução em todos os frames.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-stopwatch"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">FPS Ideal</h6>
                                    <p class="text-muted small mb-0">24-30 FPS para movimento suave. 12-15 FPS para efeitos dramáticos. Teste diferentes valores para encontrar o melhor resultado.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-sort-numeric-down"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">Sequência Correta</h6>
                                    <p class="text-muted small mb-0">Organize os frames na ordem cronológica correta. Use nomes de arquivo sequenciais para facilitar a organização.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">Configuração de Câmera</h6>
                                    <p class="text-muted small mb-0">Use tripé e configurações manuais fixas. Evite mudanças de exposição, foco ou zoom durante a captura.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">Intervalo de Captura</h6>
                                    <p class="text-muted small mb-0">Para construções: 1 foto por hora. Para paisagens: 1 foto a cada 30-60 segundos. Ajuste conforme a velocidade do evento.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="tip-item">
                            <div class="d-flex align-items-start">
                                <div class="tip-icon me-3">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-2">Pós-Processamento</h6>
                                    <p class="text-muted small mb-0">Aplique correções de cor e exposição antes do upload. Use formatos JPEG para otimizar o tempo de processamento.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 