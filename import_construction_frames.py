#!/usr/bin/env python3
"""
Script para importar os frames de construção extraídos do YouTube
para o sistema EagleView como um projeto de exemplo.
"""

import os
import sys
import django
from pathlib import Path
import shutil
from datetime import datetime

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eagleview.settings')
django.setup()

from django.contrib.auth.models import User
from videos.models import Timelapse, Frame

def import_construction_frames():
    """
    Importa os frames de construção para o sistema EagleView
    """
    
    frames_dir = Path("media/construction_frames")
    target_dir = Path("media/frames")
    
    if not frames_dir.exists():
        print("❌ Diretório de frames não encontrado:", frames_dir)
        return False
    
    # Criar diretório de destino se não existir
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # Obter ou criar usuário admin
    try:
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            user = User.objects.filter(is_staff=True).first()
        if not user:
            print("❌ Nenhum usuário admin encontrado. Crie um superusuário primeiro.")
            return False
    except Exception as e:
        print(f"❌ Erro ao buscar usuário: {e}")
        return False
    
    print(f"👤 Usando usuário: {user.username}")
    
    # Criar projeto de timelapse
    try:
        timelapse = Timelapse.objects.create(
            title="Construção de Casa - Exemplo YouTube",
            description="Timelapse de construção de uma casa extraído do YouTube. "
                       "Este é um projeto de exemplo com 444 frames mostrando "
                       "o progresso de construção ao longo de 6 meses.",
            created_by=user,
            fps=30,
            duration=15  # Duração estimada em segundos
        )
        print(f"✅ Projeto criado: {timelapse.title} (ID: {timelapse.id})")
    except Exception as e:
        print(f"❌ Erro ao criar projeto: {e}")
        return False
    
    # Importar frames
    frame_files = sorted(list(frames_dir.glob("frame_*.jpg")))
    total_frames = len(frame_files)
    
    print(f"📸 Importando {total_frames} frames...")
    
    imported_count = 0
    for i, frame_file in enumerate(frame_files, 1):
        try:
            # Criar nome único para o frame
            new_filename = f"construction_{timelapse.id}_{i:04d}.jpg"
            target_path = target_dir / new_filename
            
            # Copiar arquivo
            shutil.copy2(frame_file, target_path)
            
            # Criar registro no banco
            frame = Frame.objects.create(
                timelapse=timelapse,
                image=f"frames/{new_filename}",
                order=i,
                timestamp=datetime.now()
            )
            
            imported_count += 1
            
            # Mostrar progresso a cada 50 frames
            if i % 50 == 0 or i == total_frames:
                progress = (i / total_frames) * 100
                print(f"   📊 Progresso: {i}/{total_frames} ({progress:.1f}%)")
                
        except Exception as e:
            print(f"⚠️  Erro ao importar frame {frame_file.name}: {e}")
            continue
    
    print(f"✅ Importação concluída!")
    print(f"📊 Frames importados: {imported_count}/{total_frames}")
    
    # Atualizar estatísticas do projeto
    try:
        timelapse.refresh_from_db()
        frame_count = timelapse.frames.count()
        print(f"📈 Total de frames no projeto: {frame_count}")
        
        if frame_count > 0:
            # Calcular duração estimada (assumindo 30 fps)
            estimated_duration = frame_count / timelapse.fps
            timelapse.duration = estimated_duration
            timelapse.save()
            print(f"⏱️  Duração estimada: {estimated_duration:.1f} segundos")
    except Exception as e:
        print(f"⚠️  Erro ao atualizar estatísticas: {e}")
    
    return True

def main():
    print("🎬 EagleView - Importador de Frames de Construção")
    print("=" * 60)
    print("📁 Importando frames do YouTube para o sistema...")
    print()
    
    success = import_construction_frames()
    
    if success:
        print()
        print("🎉 Importação concluída com sucesso!")
        print("💡 Agora você pode:")
        print("   1. Acessar o sistema EagleView")
        print("   2. Ir para 'Meus Projetos'")
        print("   3. Encontrar o projeto 'Construção de Casa - Exemplo YouTube'")
        print("   4. Selecionar os frames desejados")
        print("   5. Gerar seu timelapse personalizado!")
        print()
        print("🔗 Acesse: http://127.0.0.1:8000/")
    else:
        print("❌ Falha na importação")
        sys.exit(1)

if __name__ == "__main__":
    main()
