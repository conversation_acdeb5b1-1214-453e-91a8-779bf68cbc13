#!/usr/bin/env python3
"""
Script para extrair frames de um vídeo de construção baixado do YouTube
para uso no sistema EagleView.
"""

import os
import subprocess
import sys
from pathlib import Path

def extract_frames_from_video(video_path, output_dir, fps=1):
    """
    Extrai frames de um vídeo usando ffmpeg
    
    Args:
        video_path (str): Caminho para o arquivo de vídeo
        output_dir (str): <PERSON>retório onde salvar os frames
        fps (float): Frames por segundo a extrair (1 = 1 frame por segundo)
    """
    
    # Criar diretório de saída se não existir
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Comando ffmpeg para extrair frames
    cmd = [
        'ffmpeg',
        '-i', video_path,
        '-vf', f'fps={fps}',  # Extrair 1 frame por segundo
        '-q:v', '2',  # Alta qualidade
        '-f', 'image2',
        os.path.join(output_dir, 'frame_%04d.jpg')
    ]
    
    print(f"Extraindo frames de {video_path}...")
    print(f"Salvando em: {output_dir}")
    print(f"Taxa: {fps} frame(s) por segundo")
    
    try:
        # Executar comando ffmpeg
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Frames extraídos com sucesso!")
        
        # Contar quantos frames foram criados
        frame_files = list(Path(output_dir).glob('frame_*.jpg'))
        print(f"📸 Total de frames extraídos: {len(frame_files)}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao extrair frames: {e}")
        print(f"Saída do erro: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ FFmpeg não encontrado. Instale o FFmpeg primeiro.")
        print("Download: https://ffmpeg.org/download.html")
        return False

def get_video_info(video_path):
    """
    Obtém informações sobre o vídeo usando ffprobe
    """
    cmd = [
        'ffprobe',
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        '-show_streams',
        video_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        import json
        info = json.loads(result.stdout)
        
        # Encontrar stream de vídeo
        video_stream = None
        for stream in info['streams']:
            if stream['codec_type'] == 'video':
                video_stream = stream
                break
        
        if video_stream:
            duration = float(info['format']['duration'])
            width = video_stream['width']
            height = video_stream['height']
            fps = eval(video_stream['r_frame_rate'])  # Converte fração para decimal
            
            print(f"📹 Informações do vídeo:")
            print(f"   Duração: {duration:.1f} segundos ({duration/60:.1f} minutos)")
            print(f"   Resolução: {width}x{height}")
            print(f"   FPS original: {fps:.2f}")
            
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'fps': fps
            }
    except Exception as e:
        print(f"⚠️  Não foi possível obter informações do vídeo: {e}")
        return None

def main():
    video_file = "construction_timelapse.mp4"
    frames_dir = "media/construction_frames"
    
    # Verificar se o arquivo de vídeo existe
    if not os.path.exists(video_file):
        print(f"❌ Arquivo de vídeo não encontrado: {video_file}")
        sys.exit(1)
    
    print("🎬 EagleView - Extrator de Frames")
    print("=" * 50)
    
    # Obter informações do vídeo
    video_info = get_video_info(video_file)
    
    if video_info:
        estimated_frames = int(video_info['duration'])  # 1 frame por segundo
        print(f"📊 Frames estimados (1 fps): {estimated_frames}")
    
    print()
    
    # Extrair frames
    success = extract_frames_from_video(
        video_path=video_file,
        output_dir=frames_dir,
        fps=1  # 1 frame por segundo
    )
    
    if success:
        print()
        print("🎉 Processo concluído!")
        print(f"📁 Frames salvos em: {frames_dir}")
        print("💡 Agora você pode usar estes frames no sistema EagleView")
        print("   para criar timelapses personalizados!")
    else:
        print("❌ Falha na extração de frames")
        sys.exit(1)

if __name__ == "__main__":
    main()
