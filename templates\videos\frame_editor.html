{% extends 'base.html' %}

{% block title %}{{ page_title }} - EagleView{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active">Editor de Frames</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-5">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="fas fa-play me-3 text-primary float"></i>
                    {{ page_title }}
                </h1>
                <p class="text-muted mb-0">Crie timelapses profissionais a partir de suas imagens</p>
            </div>
            <div class="d-flex gap-2">
                {% if timelapse %}
                    <a href="{% url 'timelapse_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Voltar aos Projetos
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Seleção/Criação de Timelapse Moderna -->
{% if not timelapse %}
<div class="row mb-5">
    <div class="col-lg-6 mb-4">
        <div class="card create-card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <i class="fas fa-plus me-3 text-success"></i>
                    Criar Novo Timelapse
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            Título do Projeto
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="title" 
                               name="title" 
                               placeholder="Ex: Construção do Edifício Principal, Pôr do Sol na Praia..."
                               required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Use um nome descritivo para facilitar a organização
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" name="create_timelapse" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            Criar Novo Projeto
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card recent-card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <i class="fas fa-history me-3 text-info"></i>
                    Projetos Recentes
                </h5>
            </div>
            <div class="card-body">
                {% if recent_timelapses %}
                    <div class="recent-projects">
                        {% for tl in recent_timelapses %}
                        <a href="{% url 'frame_editor_with_id' tl.id %}" 
                           class="recent-project-item d-flex align-items-center p-3 rounded mb-2">
                            <div class="project-icon me-3">
                                {% if tl.video %}
                                    <i class="fas fa-video text-success"></i>
                                {% elif tl.frames.count > 0 %}
                                    <i class="fas fa-clock text-warning"></i>
                                {% else %}
                                    <i class="fas fa-upload text-muted"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">{{ tl.title }}</h6>
                                <small class="text-muted">
                                    {{ tl.frames.count }} frame{{ tl.frames.count|pluralize }} • 
                                    {{ tl.created_at|timesince }} atrás
                                </small>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </a>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'timelapse_list' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-folder-open me-2"></i>
                            Ver Todos os Projetos
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Nenhum projeto criado ainda</h6>
                        <p class="text-muted small mb-0">Crie seu primeiro timelapse agora!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Editor Principal (quando um timelapse está selecionado) -->
{% if timelapse %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-primary d-flex justify-content-between align-items-center">
            <div>
                <strong>Editando:</strong> {{ timelapse.title }}
                <span class="badge bg-secondary ms-2">{{ frames.count }} frames</span>
            </div>
            <a href="{% url 'frame_editor' %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<!-- Upload de Imagens Moderno -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card upload-card">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <i class="fas fa-cloud-upload-alt me-3 text-primary"></i>
                    Upload de Imagens
                </h5>
            </div>
            <div class="card-body">
                <!-- Área de Drag & Drop Principal -->
                <div id="drop-area" class="drop-area mb-4">
                    <div class="text-center py-5">
                        <div class="upload-icon mb-4">
                            <i class="fas fa-cloud-upload-alt fa-4x text-primary"></i>
                        </div>
                        <h4 class="text-light mb-3">Arraste suas imagens aqui</h4>
                        <p class="text-muted mb-4">ou clique no botão abaixo para selecionar arquivos</p>
                        
                        <form method="post" enctype="multipart/form-data" id="upload-form" class="d-inline-block">
                            {% csrf_token %}
                            <input type="file" 
                                   class="d-none" 
                                   id="images" 
                                   name="images" 
                                   multiple 
                                   accept="image/*">
                            <label for="images" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-folder-open me-2"></i>
                                Selecionar Imagens
                            </label>
                            <button type="submit" name="upload_images" class="btn btn-success btn-lg" id="upload-btn" disabled>
                                <i class="fas fa-upload me-2"></i>
                                Enviar Arquivos
                            </button>
                        </form>
                        
                        <div class="mt-4">
                            <div class="row g-3 text-center">
                                <div class="col-md-4">
                                    <div class="upload-tip">
                                        <i class="fas fa-images text-info mb-2"></i>
                                        <small class="text-muted d-block">JPG, PNG, GIF</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="upload-tip">
                                        <i class="fas fa-layer-group text-warning mb-2"></i>
                                        <small class="text-muted d-block">Até 50 arquivos</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="upload-tip">
                                        <i class="fas fa-expand-arrows-alt text-success mb-2"></i>
                                        <small class="text-muted d-block">Mín. 1080p</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Barra de Progresso -->
                <div id="upload-progress" class="mt-4" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            Enviando imagens...
                        </h6>
                        <span id="progress-text" class="text-muted">0%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-2 d-block" id="upload-status">Preparando upload...</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Galeria de Frames -->
{% if frames %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i>
                    Frames do Timelapse ({{ frames.count }} imagens)
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="selectAllFrames()">
                        Selecionar Todos
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="deselectAllFrames()">
                        Desmarcar Todos
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="post" id="selection-form">
                    {% csrf_token %}
                    <div class="frame-gallery">
                        {% for frame in frames %}
                        <div class="frame-item {% if frame.selected %}selected{% endif %}" 
                             data-frame-id="{{ frame.id }}"
                             onclick="toggleFrameSelection(this)">
                            <img src="{{ frame.image.url }}" 
                                 alt="{{ frame.filename }}"
                                 loading="lazy">
                            <div class="frame-overlay">
                                <div class="frame-info">
                                    <div class="frame-filename">{{ frame.filename|truncatechars:20 }}</div>
                                    <div class="frame-order">{{ frame.order }}</div>
                                </div>
                                <div class="frame-checkbox">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                            <input type="checkbox" 
                                   name="selected_frames" 
                                   value="{{ frame.id }}"
                                   {% if frame.selected %}checked{% endif %}
                                   style="display: none;">
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="mt-3 d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" name="update_selection" class="btn btn-outline-primary">
                                <i class="fas fa-save me-2"></i>
                                Salvar Seleção
                            </button>
                            <span class="ms-3 text-muted">
                                <span id="selected-count">{{ frames|length }}</span> frames selecionados
                            </span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Configurações e Geração -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Gerar Timelapse
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="generate-form">
                    {% csrf_token %}
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="fps" class="form-label">FPS (Frames por Segundo)</label>
                                <select class="form-select" id="fps" name="fps">
                                    <option value="5">5 FPS (Muito Lento)</option>
                                    <option value="10" selected>10 FPS (Lento)</option>
                                    <option value="15">15 FPS (Normal)</option>
                                    <option value="24">24 FPS (Rápido)</option>
                                    <option value="30">30 FPS (Muito Rápido)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Duração Estimada</label>
                                <div class="form-control-plaintext" id="duration-estimate">
                                    ~<span id="duration-seconds">0</span> segundos
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Frames Selecionados</label>
                                <div class="form-control-plaintext">
                                    <span id="generate-selected-count">{{ frames|length }}</span> frames
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" 
                                        name="generate_video" 
                                        class="btn btn-success w-100"
                                        id="generate-btn">
                                    <i class="fas fa-play me-2"></i>
                                    Gerar Timelapse
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Indicador de Processamento -->
                <div id="processing-indicator" class="mt-3" style="display: none;">
                    <div class="alert alert-info d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <div>
                            <strong>Processando timelapse...</strong><br>
                            <small class="text-muted">Isso pode levar alguns minutos dependendo do número de frames.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if not timelapse or not frames %}
<!-- Dicas -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <h6 class="alert-heading">
                <i class="fas fa-lightbulb me-2"></i>
                Dicas para Melhores Resultados
            </h6>
            <ul class="mb-0">
                <li><strong>Resolução:</strong> Use imagens com pelo menos 1920x1080 pixels para qualidade HD</li>
                <li><strong>Formato:</strong> JPG é recomendado para menor tamanho de arquivo</li>
                <li><strong>Sequência:</strong> Nomeie os arquivos em ordem cronológica (ex: img001.jpg, img002.jpg)</li>
                <li><strong>FPS:</strong> Para timelapses longos, use FPS menor (5-10). Para ação rápida, use FPS maior (24-30)</li>
                <li><strong>Iluminação:</strong> Mantenha configurações consistentes da câmera para melhor resultado</li>
            </ul>
        </div>
    </div>
</div>
{% endif %}
{% endif %}

<style>
.drop-area {
    border: 2px dashed var(--bs-border-color);
    border-radius: 0.375rem;
    background-color: var(--bs-gray-100);
    transition: all 0.3s ease;
}

.drop-area.dragover {
    border-color: var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle);
}

.frame-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.frame-item {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    background: var(--bs-body-bg);
}

.frame-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.frame-item.selected {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.25);
}

.frame-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.frame-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: end;
}

.frame-info {
    flex: 1;
}

.frame-filename {
    font-size: 0.8rem;
    font-weight: 500;
}

.frame-order {
    font-size: 0.7rem;
    opacity: 0.8;
}

.frame-checkbox {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.frame-item.selected .frame-checkbox {
    opacity: 1;
}

.upload-progress {
    margin-top: 1rem;
}

#processing-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>

<script>
// Controle de seleção de frames
function toggleFrameSelection(element) {
    const checkbox = element.querySelector('input[type="checkbox"]');
    const isSelected = checkbox.checked;
    
    checkbox.checked = !isSelected;
    element.classList.toggle('selected', !isSelected);
    
    updateSelectedCount();
}

function selectAllFrames() {
    document.querySelectorAll('.frame-item').forEach(item => {
        item.classList.add('selected');
        item.querySelector('input[type="checkbox"]').checked = true;
    });
    updateSelectedCount();
}

function deselectAllFrames() {
    document.querySelectorAll('.frame-item').forEach(item => {
        item.classList.remove('selected');
        item.querySelector('input[type="checkbox"]').checked = false;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCount = document.querySelectorAll('.frame-item.selected').length;
    document.querySelectorAll('#selected-count, #generate-selected-count').forEach(el => {
        el.textContent = selectedCount;
    });
    
    // Atualizar duração estimada
    const fps = parseInt(document.getElementById('fps')?.value || 10);
    const duration = Math.round(selectedCount / fps * 10) / 10;
    const durationEl = document.getElementById('duration-seconds');
    if (durationEl) {
        durationEl.textContent = duration;
    }
}

// Drag and drop
document.addEventListener('DOMContentLoaded', function() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('images');
    
    if (dropArea && fileInput) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        function highlight(e) {
            dropArea.classList.add('dragover');
        }
        
        function unhighlight(e) {
            dropArea.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            fileInput.files = files;
            
            // Auto-submit form
            document.getElementById('upload-form').submit();
        }
    }
    
    // Atualizar contadores ao carregar
    updateSelectedCount();
    
    // Inicializar todos os frames como selecionados se há frames
    const frameItems = document.querySelectorAll('.frame-item');
    if (frameItems.length > 0) {
        frameItems.forEach(item => {
            item.classList.add('selected');
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        updateSelectedCount();
    }
    
    // Atualizar duração quando FPS muda
    const fpsSelect = document.getElementById('fps');
    if (fpsSelect) {
        fpsSelect.addEventListener('change', updateSelectedCount);
    }
    
    // Indicador de processamento
    const generateForm = document.getElementById('generate-form');
    if (generateForm) {
        generateForm.addEventListener('submit', function() {
            const indicator = document.getElementById('processing-indicator');
            const button = document.getElementById('generate-btn');
            if (indicator && button) {
                indicator.style.display = 'block';
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processando...';
            }
        });
    }
});
</script>
{% endblock %} 